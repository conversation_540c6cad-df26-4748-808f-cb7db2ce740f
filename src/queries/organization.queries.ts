import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { authClient } from "@/libs/auth-client";
import { useUser } from "@/queries/user.queries";
import { api } from "@/trpc/react";
import type { Role } from "@/types/organization.types";
import { isEmpty } from "@/utils/is-empty";
import { useMutation } from "@tanstack/react-query";
import { forbidden, notFound, useRouter } from "next/navigation";
import { toast } from "sonner";

export const useOrganizations = () => {
  return api.organizations.getAll.useQuery({ take: 500 });
};

export const useOrganizationMembers = (orgId: string) => {
  return api.organizations.getMembers.useQuery({ id: orgId });
};

export const useOrganizationInvites = (orgId: string) => {
  return api.organizations.getInvites.useQuery({ id: orgId });
};

export const useOrganizationById = (id: string) => {
  return api.organizations.getById.useQuery({ id }, { enabled: !isEmpty(id) });
};

export const useOrganizationBySlug = (slug: string) => {
  const response = api.organizations.getBySlug.useQuery({ slug });
  if (response.error && response.error.data?.code === "FORBIDDEN") {
    return forbidden();
  }
  if (response.error && response.error.data?.code === "NOT_FOUND") {
    return notFound();
  }
  return response;
};

export const useOrganizationMemberRole = () => {
  const user = useUser();
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);
  const memberRole = api.organizations.getMemberRole.useQuery(
    { id: user.data?.id ?? "", orgId: organization.data?.id ?? "" },
    {
      enabled: !isEmpty(user.data?.id) && !isEmpty(organization.data?.id),
    },
  );
  return memberRole.data?.role as Role;
};

export const useOrganizationAddMutation = () => {
  const router = useRouter();
  const apiUtils = api.useUtils();

  return api.organizations.create.useMutation({
    onSuccess: async (data) => {
      router.push(`/${data?.slug}/dashboard`);
    },
    onError: (error, _index, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getAll.invalidate();
    },
  });
};

export const useOrganizationUpdateMutation = ({
  showToast = true,
}: { showToast?: boolean } = {}) => {
  const apiUtils = api.useUtils();

  return api.organizations.updateById.useMutation({
    onSuccess: () => {
      if (showToast) {
        toast.success("Organization settings updated", { closeButton: true });
      }
    },
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, _error, input) => {
      await apiUtils.organizations.getById.invalidate({ id: input.id });
      await apiUtils.organizations.getBySlug.invalidate({ slug: data?.slug! });
      await apiUtils.organizations.getAll.invalidate();
    },
  });
};

export const useOrganizationDeleteMutation = () => {
  const apiUtils = api.useUtils();

  return api.organizations.deleteById.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getAll.invalidate();
    },
  });
};

export const useCreateOrganizationInviteMutation = () => {
  const apiUtils = api.useUtils();

  return useMutation({
    mutationFn: async (params: {
      email: string;
      role: Role;
      organizationId: string;
    }) => {
      return await authClient.organization.inviteMember({
        email: params.email,
        role: params.role,
        organizationId: params.organizationId,
        resend: true,
      });
    },
    onSuccess: async (data, input) => {
      toast.success(`Invitation successfully sent!`, { closeButton: true });
    },
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, _, input) => {
      apiUtils.organizations.getInvites.invalidate({
        id: input.organizationId,
      });
    },
  });
};

export const useOrganizationDeleteInviteMutation = () => {
  const apiUtils = api.useUtils();

  return api.organizations.deleteInvite.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getInvites.invalidate();
    },
  });
};

export const useOrganizationUpdateMemberRoleMutation = (orgId: string) => {
  const apiUtils = api.useUtils();

  return api.organizations.updateMemberRole.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getMembers.invalidate({ id: orgId });
    },
  });
};

export const useOrganizationMemberDeleteMutation = (orgId: string) => {
  const apiUtils = api.useUtils();

  return api.organizations.deleteMember.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getMembers.invalidate({ id: orgId });
    },
  });
};

export async function checkSlug(slug: string) {
  return await authClient.organization.checkSlug({ slug });
}
