"use client";

import { PermissionGuard } from "@/components/permission-guard";
import { ProjectConfirmEditDialog } from "@/components/projects/project-confirm-edit-dialog";
import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { ProjectSubmitApprovalButton } from "@/components/projects/project-submit-approval-button";
import { ProjectViewActionsMenu } from "@/components/projects/project-view-actions-menu";
import { SlidePreview } from "@/components/projects/slide-preview";
import { SlidesImageUploader } from "@/components/projects/slides-image-uploader";
import { SlidesSidebar } from "@/components/projects/slides-sidebar";
import { AlertError } from "@/components/ui/alert-error";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>oader } from "@/components/ui/page-structure";
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { cn } from "@/libs/utils";
import { useLocations } from "@/queries/location.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import {
  useProjectById,
  useProjectPublishMutation,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import { useSlideAddManyMutation, useSlides } from "@/queries/slide.queries";
import { Roles } from "@/types/organization.types";
import type { ProjectStatus } from "@/types/project.types";
import type { Slide } from "@/types/slide.types";
import { createImageUploadUrl } from "@/utils/create-image-upload-url";
import { isEmpty } from "@/utils/is-empty";
import {
  IconBuilding,
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconMapPin,
  IconPencil,
  IconPlayerPlay,
  IconPlus,
  IconSearch,
  IconSend,
} from "@tabler/icons-react";
import type { UploadedFile } from "better-upload/client";
import { useEffect, useState } from "react";

interface Props {
  projectId: string;
}

export function ProjectView({ projectId }: Props) {
  const slug = useOrganizationSlug();
  const [uploaderDialog, uploaderDialogHandlers] = useDialog();
  const [publishSheetDialog, publishSheetDialogHandlers] = useDialog();
  const [confirmEditDialog, confirmEditDialogHandlers] = useDialog();
  const [filesUploadingLength, setFilesUploadingLength] = useState(0);
  const [currentSlideId, setCurrentSlideId] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [publishError, setPublishError] = useState("");

  const [selectedLocations, setSelectedLocations] = useState<Set<string>>(
    new Set(),
  );
  const [selectedSublocations, setSelectedSublocations] = useState<Set<string>>(
    new Set(),
  );
  const [expandedLocations, setExpandedLocations] = useState<Set<string>>(
    new Set(),
  );

  const organization = useOrganizationBySlug(slug);
  const locationsQuery = useLocations({
    organizationId: organization.data?.id ?? "",
  });
  const project = useProjectById(projectId);
  const slidesQuery = useSlides(projectId);
  const [slides, setSlides] = useState<Slide[]>([]);

  const [projectName, setProjectName] = useDebouncedState(
    project.data?.name,
    250,
  );

  const projectUpdateMutation = useProjectUpdateMutation();
  const publishProjectMutation = useProjectPublishMutation();

  const isProjectNotEditing = project.data?.status !== "editing";

  // This is used to update the project name when the the debounced projectName state is updated
  useEffect(() => {
    async function updateProjectName() {
      return await projectUpdateMutation.mutateAsync({
        id: projectId,
        name: projectName,
      });
    }
    if (!isEmpty(projectName)) {
      updateProjectName();
    }
  }, [projectName]);

  // This is used to update the slides when the slides query is updated
  useEffect(() => {
    if (slidesQuery.data?.data) {
      setSlides(slidesQuery.data?.data);
    }
  }, [slidesQuery.data?.data]);

  const currentSlideById =
    slides.find((slide) => slide.id === currentSlideId) ?? slides[0];

  const addSlidesMutation = useSlideAddManyMutation();

  const addNewSlides = async (files: UploadedFile[]) => {
    uploaderDialogHandlers.close();
    setFilesUploadingLength(files.length);
    const slidesToAdd = files.map((file) => ({
      imageUrl: createImageUploadUrl(file.objectKey),
      objectKey: file.objectKey,
    }));
    await addSlidesMutation.mutateAsync({
      projectId,
      lastSlideId: slides.at(-1)?.id ?? "",
      images: slidesToAdd,
    });
    setCurrentSlideId(slides.at(-1)?.id ?? "");
  };

  // Filter locations based on the search query
  const filteredLocations =
    locationsQuery?.data?.data?.filter(
      (location) =>
        location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.state.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.postalCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.id.includes(searchQuery) ||
        location.sublocations?.some((sub) =>
          sub.name.toLowerCase().includes(searchQuery.toLowerCase()),
        ),
    ) ?? [];

  // Handle location expansion
  const toggleLocationExpanded = (locationId: string) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationId)) {
      newExpanded.delete(locationId);
    } else {
      newExpanded.add(locationId);
    }
    setExpandedLocations(newExpanded);
  };

  // Handle location selection
  const handleLocationSelect = (locationId: string, checked: boolean) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);
    const newExpandedLocations = new Set(expandedLocations);

    if (checked) {
      newSelectedLocations.add(locationId);
      // Automatically expand when location is selected
      newExpandedLocations.add(locationId);
      // When selecting a location, remove any individual sublocations for that location
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      location?.sublocations?.forEach((sub) => {
        newSelectedSublocations.delete(sub.id);
      });
    } else {
      newSelectedLocations.delete(locationId);
      // When deselecting a location, also remove all its sublocations
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      location?.sublocations?.forEach((sub) => {
        newSelectedSublocations.delete(sub.id);
      });
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
    setExpandedLocations(newExpandedLocations);
  };

  // Handle sublocation selection
  const handleSublocationSelect = (
    locationId: string,
    sublocationId: string,
    checked: boolean,
  ) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);

    if (checked) {
      newSelectedSublocations.add(sublocationId);
      // Always select the parent location when any sublocation is selected
      newSelectedLocations.add(locationId);
    } else {
      newSelectedSublocations.delete(sublocationId);
      // Check if any other sublocations for this location are still selected
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      const hasOtherSelectedSublocations = location?.sublocations?.some(
        (sub) =>
          sub.id !== sublocationId && newSelectedSublocations.has(sub.id),
      );
      // Only deselect the location if no sublocations are selected
      if (!hasOtherSelectedSublocations) {
        newSelectedLocations.delete(locationId);
      }
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
  };

  // Check if location is selected
  const isLocationSelected = (locationId: string) => {
    return selectedLocations.has(locationId);
  };

  // Check if sublocation is selected
  const isSublocationSelected = (locationId: string, sublocationId: string) => {
    // Check if sublocation is specifically selected
    return selectedSublocations.has(sublocationId);
  };

  // Check if location has any selected sublocations
  const hasSelectedSublocations = (locationId: string) => {
    const location = locationsQuery?.data?.data?.find(
      (loc) => loc.id === locationId,
    );
    return location?.sublocations?.some((sub) =>
      selectedSublocations.has(sub.id),
    );
  };

  // Handle select all functionality
  const isAllSelected =
    filteredLocations.length > 0 &&
    filteredLocations.every((location) => selectedLocations.has(location.id));

  const handleSelectAll = (checked: boolean) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);

    if (checked) {
      filteredLocations.forEach((location) => {
        newSelectedLocations.add(location.id);
        // Remove any individual sublocations when selecting all locations
        location.sublocations?.forEach((sub) => {
          newSelectedSublocations.delete(sub.id);
        });
      });
    } else {
      filteredLocations.forEach((location) => {
        newSelectedLocations.delete(location.id);
        // Also remove any sublocations
        location.sublocations?.forEach((sub) => {
          newSelectedSublocations.delete(sub.id);
        });
      });
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
  };

  function validatePublishValues() {
    if (!startDate && !endDate) {
      return {
        error: true,
        errorMessage: "Please select a start and end date",
      };
    }
    if (startDate && endDate && startDate > endDate) {
      return {
        error: true,
        errorMessage: "Start date must be before end date",
      };
    }
    if (selectedLocations.size === 0 && selectedSublocations.size === 0) {
      return {
        error: true,
        errorMessage: "Please select at least one location or sublocation",
      };
    }
    return { error: false, errorMessage: "" };
  }

  async function handlePublishProject() {
    setPublishError("");

    const validation = validatePublishValues();
    if (validation.error) {
      setPublishError(validation.errorMessage);
      return;
    }

    const data = {
      id: projectId,
      startDate: startDate?.toISOString() ?? "",
      endDate: endDate?.toISOString() ?? "",
      locations: Array.from(selectedLocations),
      sublocations: Array.from(selectedSublocations),
    };

    await publishProjectMutation.mutateAsync(data);
  }

  function closePublishSheetDialog() {
    setPublishError("");
    setSelectedLocations(new Set());
    setSelectedSublocations(new Set());
    setExpandedLocations(new Set());
    setSearchQuery("");
    setStartDate(null);
    setEndDate(null);
    publishSheetDialogHandlers.close();
  }

  if (project.isLoading || slidesQuery.isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="relative flex h-full flex-col overflow-y-hidden">
      <header className="absolute top-0 right-0 left-0 flex h-14 items-center justify-between border-b px-4 lg:px-4">
        <div className="flex items-center gap-4">
          <Button
            size="icon"
            href={`/${slug}/projects`}
            variant="secondary"
            className="size-8"
          >
            <IconChevronLeft size={16} />
          </Button>
          <Input
            defaultValue={project?.data?.name}
            className="w-full rounded-none border-0 p-0 text-xl font-semibold shadow-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:opacity-100 sm:w-[500px] sm:text-xl"
            disabled={isProjectNotEditing}
            onChange={(e) => {
              setProjectName(e.target.value);
            }}
          />
        </div>
        <div className="flex items-center gap-2">
          <ProjectStatusBadge
            status={project?.data?.status as ProjectStatus}
            className="mr-2"
          />
          {isProjectNotEditing ? (
            <Button
              variant="outline"
              leftIcon={<IconPencil size={16} />}
              onClick={confirmEditDialogHandlers.open}
            >
              Edit project
            </Button>
          ) : (
            <Button
              onClick={uploaderDialogHandlers.open}
              leftIcon={<IconPlus size={16} />}
            >
              Add Slides
            </Button>
          )}
          <Button
            variant="outline"
            leftIcon={<IconPlayerPlay size={16} />}
            href={`/secure/preview?projectId=${projectId}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            View preview
          </Button>
          {!isProjectNotEditing && (
            <Sheet
              open={publishSheetDialog}
              onOpenChange={closePublishSheetDialog}
            >
              <Button
                variant="outline"
                leftIcon={<IconSend size={16} />}
                onClick={publishSheetDialogHandlers.open}
                disabled={slides.length === 0}
              >
                Publish
              </Button>

              <SheetContent className="flex h-full flex-col sm:max-w-[600px]">
                <SheetHeader className="flex-shrink-0">
                  <SheetTitle className="text-xl">
                    Schedule your project to be published
                  </SheetTitle>
                  <SheetDescription className="text-base">
                    Publishing your project will make it available to the public
                    and will be available to view in your store locations and
                    sublocations.
                  </SheetDescription>
                </SheetHeader>

                <div className="flex flex-1 flex-col overflow-hidden px-4">
                  <AlertError message={publishError} className="mb-4" />

                  <div className="flex-shrink-0">
                    <p className="text-base font-semibold">Project runtime</p>
                    <div className="mt-2 flex w-full items-center justify-between gap-3">
                      <DatePicker
                        label="Start date"
                        className="w-full"
                        value={startDate}
                        onSelect={(date) => setStartDate(date ?? null)}
                        error={!!startDate && !!endDate && startDate > endDate}
                        errorMessage="Start date must be before end date"
                      />
                      <DatePicker
                        label="End date"
                        className="w-full"
                        value={endDate}
                        onSelect={(date) => setEndDate(date ?? null)}
                        error={!!startDate && !!endDate && startDate > endDate}
                        errorMessage="End date must be after start date"
                      />
                    </div>
                  </div>

                  <div className="mt-8 flex flex-1 flex-col overflow-hidden">
                    <div className="flex-shrink-0">
                      <p className="text-base font-semibold">
                        Choose locations and sublocations
                      </p>
                      <div className="mt-3">
                        <Input
                          className="w-full"
                          icon={<IconSearch size={16} />}
                          placeholder="Filter locations and sublocations"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                      <div className="mt-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id="select-all"
                            checked={isAllSelected}
                            onCheckedChange={handleSelectAll}
                          />
                          <Label htmlFor="select-all">
                            Select all locations
                          </Label>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 flex-1 overflow-y-auto">
                      <div className="space-y-3">
                        {filteredLocations.map((location) => (
                          <div
                            key={location.id}
                            className={cn(
                              `cursor-pointer rounded-lg border transition-colors ${
                                isLocationSelected(location.id) ||
                                hasSelectedSublocations(location.id)
                                  ? "border-gray-200 bg-gray-50 hover:bg-gray-100"
                                  : "border-gray-200 bg-card hover:bg-gray-50"
                              }`,
                            )}
                            onClick={() =>
                              handleLocationSelect(
                                location.id,
                                !isLocationSelected(location.id),
                              )
                            }
                          >
                            {/* Location Header */}
                            <div className="flex items-center gap-3 p-3">
                              <Checkbox
                                id={`location-${location.id}`}
                                checked={isLocationSelected(location.id)}
                                onCheckedChange={(checked) =>
                                  handleLocationSelect(location.id, !!checked)
                                }
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center gap-2">
                                  <IconMapPin
                                    size={16}
                                    className="flex-shrink-0 text-gray-500"
                                  />
                                  <div className="font-medium text-card-foreground">
                                    {location.name}
                                  </div>
                                </div>
                                <div className="mt-1 text-xs text-muted-foreground">
                                  {location.address} {location.city},{" "}
                                  {location.state}
                                </div>
                              </div>
                              {location.sublocations &&
                                location.sublocations.length > 0 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleLocationExpanded(location.id);
                                    }}
                                    className="h-8 w-8 p-0"
                                  >
                                    {expandedLocations.has(location.id) ? (
                                      <IconChevronDown size={16} />
                                    ) : (
                                      <IconChevronRight size={16} />
                                    )}
                                  </Button>
                                )}
                            </div>

                            {/* Sublocations */}
                            {location.sublocations &&
                              location.sublocations.length > 0 &&
                              expandedLocations.has(location.id) && (
                                <div className="border-t border-gray-200 px-3 py-2">
                                  {location.sublocations.map((sublocation) => (
                                    <div
                                      key={sublocation.id}
                                      className={cn(
                                        `flex cursor-pointer items-center gap-3 rounded py-2 pl-6 transition-colors ${
                                          isSublocationSelected(
                                            location.id,
                                            sublocation.id,
                                          )
                                            ? "bg-gray-100 hover:bg-gray-200"
                                            : "hover:bg-gray-50"
                                        }`,
                                      )}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSublocationSelect(
                                          location.id,
                                          sublocation.id,
                                          !isSublocationSelected(
                                            location.id,
                                            sublocation.id,
                                          ),
                                        );
                                      }}
                                    >
                                      <Checkbox
                                        id={`sublocation-${sublocation.id}`}
                                        checked={isSublocationSelected(
                                          location.id,
                                          sublocation.id,
                                        )}
                                        onCheckedChange={(checked) =>
                                          handleSublocationSelect(
                                            location.id,
                                            sublocation.id,
                                            !!checked,
                                          )
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                      <div className="flex flex-1 items-center gap-2">
                                        <IconBuilding
                                          size={14}
                                          className="text-gray-400"
                                        />
                                        <span className="text-sm text-gray-700">
                                          {sublocation.name}
                                        </span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <SheetFooter className="flex-shrink-0 border-t border-gray-200 p-4 sm:flex-row">
                  <PermissionGuard
                    allowedRoles={[Roles.ADMIN]}
                    fallback={
                      <ProjectSubmitApprovalButton
                        projectId={projectId}
                        projectName={project.data?.name || ""}
                        startDate={startDate}
                        endDate={endDate}
                        selectedLocations={selectedLocations}
                        selectedSublocations={selectedSublocations}
                      />
                    }
                  >
                    <Button onClick={handlePublishProject}>
                      Publish project
                    </Button>
                  </PermissionGuard>
                  <SheetClose asChild>
                    <Button variant="outline">Cancel</Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          )}

          <ProjectViewActionsMenu projectId={projectId} />
        </div>
      </header>

      <div className="absolute top-14 right-0 bottom-0 left-0 flex flex-1 overflow-hidden">
        <SlidesSidebar
          slides={slides}
          projectStatus={project.data?.status as ProjectStatus}
          currentSlideId={currentSlideId || (currentSlideById?.id ?? "")}
          onSelectSlideById={setCurrentSlideId}
          isPending={addSlidesMutation.isPending}
          slidesUploadingLength={filesUploadingLength}
        />
        <>
          {isEmpty(slides) && (
            <div className="flex h-full w-full flex-col items-center justify-center">
              <EmptyState
                title="No slides yet"
                subtitle="Add some new slides to get started."
                icon={<IconPlus size={40} />}
                actionButton={
                  <Button
                    leftIcon={<IconPlus size={16} />}
                    onClick={uploaderDialogHandlers.open}
                  >
                    Add Slides
                  </Button>
                }
              />
            </div>
          )}
          {!isEmpty(slides) && (
            <main className="flex flex-1 flex-col overflow-y-auto p-6">
              <SlidePreview slide={currentSlideById} />
            </main>
          )}
        </>
      </div>
      <SlidesImageUploader
        open={uploaderDialog}
        onClose={uploaderDialogHandlers.close}
        onUploadComplete={addNewSlides}
        organizationId={organization.data?.id ?? ""}
      />
      <ProjectConfirmEditDialog
        open={confirmEditDialog}
        onClose={confirmEditDialogHandlers.close}
        projectId={projectId}
      />
    </div>
  );
}
