"use client";

import { ProjectCard } from "@/components/projects/project-card";
import { ProjectCreateDialog } from "@/components/projects/project-create-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import {
  <PERSON><PERSON>ontent,
  PageLoader,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { SearchInput } from "@/components/ui/search-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import { useInfiniteProjects } from "@/queries/project.queries";
import type {
  InfiniteProjectsData,
  ProjectStatus,
} from "@/types/project.types";
import { isEmpty } from "@/utils/is-empty";
import { IconFolder, IconPlus } from "@tabler/icons-react";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";

type ProjectStatusFilter = "all" | ProjectStatus;

const formatProjects = (projects?: InfiniteProjectsData) =>
  projects?.pages.flatMap((page) => page.data) ?? [];

export function ProjectsView() {
  const slug = useOrganizationSlug();
  const [searchString, setSearchString] = useDebouncedState("", 300);
  const [myProjects, setMyProjects] = useLocalStorage("myProjects", "true");
  const [status, setStatus] = useState<ProjectStatusFilter>("all");
  const [openCreateModal, openCreateModalHandlers] = useDialog();

  const { ref, inView } = useInView();

  const organization = useOrganizationBySlug(slug);
  const userRole = useOrganizationMemberRole();
  const projects = useInfiniteProjects({
    organizationId: organization?.data?.id ?? "",
    searchString,
    status,
    myProjects: myProjects === "true",
  });

  useEffect(() => {
    if (projects.hasNextPage && inView) {
      projects.fetchNextPage();
    }
  }, [inView, projects]);

  const data = useMemo(() => formatProjects(projects.data), [projects]);

  const noSearchResults =
    (isEmpty(data) && !isEmpty(searchString)) ||
    (isEmpty(data) && status !== "all");

  const isPageLoading = projects.isLoading || organization.isLoading;

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Projects</PageTitle>
        <div>
          <Button
            leftIcon={<IconPlus size={16} />}
            onClick={openCreateModalHandlers.open}
          >
            Create new project
          </Button>
        </div>
      </div>

      <PageContent className="mt-10">
        <div className="items-center gap-2 space-y-2 md:flex md:space-y-0">
          <SearchInput
            placeholder="Search projects..."
            defaultValue={searchString}
            onChange={(e) => setSearchString(e.target.value)}
            className="w-full md:w-72"
          />
          <div className="flex items-center gap-2">
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as ProjectStatusFilter)}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="editing">Editing</SelectItem>
                <SelectItem value="pending_approval">
                  Pending Approval
                </SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={userRole === "admin" ? myProjects : "true"}
              onValueChange={setMyProjects}
              disabled={userRole !== "admin"}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by my projects" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="false">All Projects</SelectItem>
                <SelectItem value="true">My Projects</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isPageLoading && (
          <PageLoader className="absolute inset-0 flex items-center justify-center" />
        )}

        {!isPageLoading && (
          <>
            {!isEmpty(data) && (
              <div className="flex flex-1 flex-col gap-4 pt-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-5">
                  {data.map((project) => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                      orgSlug={slug}
                    />
                  ))}
                </div>
              </div>
            )}

            {isEmpty(data) && !noSearchResults && (
              <div className="py-100">
                <EmptyState
                  title="No projects yet"
                  subtitle="Create a new project to get started."
                  icon={<IconFolder size={40} />}
                  actionButton={
                    <Button
                      leftIcon={<IconPlus size={16} />}
                      onClick={openCreateModalHandlers.open}
                    >
                      Create new project
                    </Button>
                  }
                />
              </div>
            )}
            {noSearchResults && (
              <div className="py-100">
                <EmptyState
                  title="No search results"
                  subtitle="Please check the spelling or filter criteria"
                  icon={<IconFolder size={40} />}
                />
              </div>
            )}
          </>
        )}

        <div ref={ref} className="flex items-center justify-center">
          {projects.isFetchingNextPage && <Loader className="mt-5" />}
        </div>
      </PageContent>

      <ProjectCreateDialog
        open={openCreateModal}
        onClose={openCreateModalHandlers.close}
      />
    </PageWrapper>
  );
}
