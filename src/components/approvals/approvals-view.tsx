"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import {
  PageContent,
  PageLoader,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  useInfiniteApprovals,
  usePendingReviews,
  useReviewApproval,
} from "@/queries/approval.queries";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import type {
  ApprovalGetByIdOutput,
  InfiniteApprovalsData,
} from "@/types/approvals.types";
import type { ProjectApprovalStatus } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import { isEmpty } from "@/utils/is-empty";
import {
  IconClock,
  IconMessageCircle,
  IconSearch,
  IconX,
} from "@tabler/icons-react";
import { CheckCircle } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { ApprovalReviewDialog } from "./approval-review-dialog";

type ReviewMutationStatus = "approved" | "rejected";

function formatApprovals(approvals?: InfiniteApprovalsData) {
  return approvals?.pages.flatMap((page) => page.data) ?? [];
}

export function ApprovalsView() {
  const orgSlug = useOrganizationSlug();
  const [statusFilter, setStatusFilter] = useState<
    ProjectApprovalStatus | "all"
  >("all");
  const [searchString, setSearchString] = useDebouncedState("", 300);
  const [myRequests, setMyRequests] = useLocalStorage("myRequests", "true");
  const [selectedApproval, setSelectedApproval] =
    useState<ApprovalGetByIdOutput | null>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);

  const { ref, inView } = useInView();

  const { data: organization, isLoading: isLoadingOrganization } =
    useOrganizationBySlug(orgSlug);
  const userRole = useOrganizationMemberRole();
  const approvals = useInfiniteApprovals({
    organizationId: organization?.id || "",
    searchString,
    status: statusFilter === "all" ? undefined : statusFilter,
    myRequests: myRequests === "true",
  });

  const pendingReviews = usePendingReviews(organization?.id || "");

  useEffect(() => {
    if (approvals.hasNextPage && inView) {
      approvals.fetchNextPage();
    }
  }, [inView, approvals]);

  const data = useMemo(() => formatApprovals(approvals.data), [approvals]);

  const reviewMutation = useReviewApproval();

  function getStatusVariant(status: string) {
    switch (status) {
      case "pending":
        return "yellow";
      case "approved":
        return "blue";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <IconClock className="h-3 w-3" />;
      case "approved":
        return <CheckCircle className="h-3 w-3" />;
      case "rejected":
        return <IconX className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const handleReview = (approval: any) => {
    setSelectedApproval(approval);
    setReviewDialogOpen(true);
  };

  const handleReviewSubmit = async (
    approvalId: string,
    status: ReviewMutationStatus,
    comments?: string,
  ) => {
    if (selectedApproval) {
      await reviewMutation.mutateAsync(
        {
          approvalId,
          status,
          reviewComments: comments || "",
        },
        {
          onSuccess: () => {
            setReviewDialogOpen(false);
            setSelectedApproval(null);
          },
        },
      );
    }
  };

  const noSearchResults = isEmpty(data) && !isEmpty(searchString);

  const isPageLoading =
    approvals.isLoading || pendingReviews.isLoading || isLoadingOrganization;

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Approvals</PageTitle>
        {pendingReviews.data && pendingReviews.data?.length > 0 && (
          <Badge variant="destructive" className="ml-2 px-3 py-1.5">
            {pendingReviews.data.length} pending review
            {pendingReviews.data.length > 1 ? "s" : ""}
          </Badge>
        )}
      </div>

      <PageContent className="mt-10">
        <div className="space-y-6">
          {/* Filters */}
          <div className="items-center gap-2 space-y-2 md:flex md:space-y-0">
            <Input
              placeholder="Search approvals..."
              defaultValue={searchString}
              onChange={(e) => setSearchString(e.target.value)}
              icon={<IconSearch className="size-4" />}
              className="w-full md:w-72"
            />
            <div className="flex items-center gap-2">
              <Select
                value={statusFilter}
                onValueChange={(value) =>
                  setStatusFilter(value as "all" | ProjectApprovalStatus)
                }
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={myRequests}
                onValueChange={setMyRequests}
                disabled={userRole !== "admin"}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter requests" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="false">All Requests</SelectItem>
                  <SelectItem value="true">My Requests</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isPageLoading && (
            <PageLoader className="absolute inset-0 flex items-center justify-center" />
          )}
          {!isPageLoading && (
            <>
              {/* Pending Reviews Alert */}
              {pendingReviews.data && pendingReviews.data?.length > 0 && (
                <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-amber-800">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
                        <IconClock className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">
                          Pending Your Review
                        </h3>
                        <p className="text-sm font-normal text-amber-700">
                          {pendingReviews.data.length} request
                          {pendingReviews.data.length > 1 ? "s" : ""} waiting
                          for your approval
                        </p>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 pt-0">
                    {pendingReviews.data?.map((approval) => (
                      <div
                        key={approval.id}
                        className="flex items-center justify-between rounded-xl bg-white p-4 shadow-sm ring-1 ring-amber-100"
                      >
                        <div className="flex items-center gap-4">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={approval.requester.image || ""} />
                            <AvatarFallback className="bg-amber-100 font-medium text-amber-700">
                              {approval.requester.name?.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="font-semibold text-gray-900">
                              {approval.project.name}
                            </p>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <span>
                                Requested by {approval.requester.name}
                              </span>
                              <span>•</span>
                              <span>{formatDate(approval.submittedAt)}</span>
                            </div>
                          </div>
                        </div>
                        <Button onClick={() => handleReview(approval)}>
                          Review
                        </Button>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Approvals List */}
              {!isEmpty(data) && (
                <div className="space-y-4">
                  {data.map((approval) => (
                    <Card key={approval.id}>
                      <CardContent className="p-6">
                        {/* Header row: Project title, status, actions */}
                        <div className="mb-4 flex flex-wrap items-center justify-between gap-4">
                          <div className="flex min-w-0 items-center gap-4">
                            <h3 className="truncate text-xl font-semibold text-gray-900">
                              {approval.project.name}
                            </h3>
                            <Badge
                              variant={getStatusVariant(approval.status)}
                              className="flex items-center gap-1.5 px-3 py-1 text-sm font-medium"
                            >
                              {getStatusIcon(approval.status)}
                              <span className="capitalize">
                                {approval.status}
                              </span>
                            </Badge>
                          </div>
                          <div className="flex flex-shrink-0 gap-2">
                            <Button
                              variant="outline"
                              href={`/${orgSlug}/projects/${approval.project.id}`}
                              className="whitespace-nowrap"
                            >
                              View Project
                            </Button>
                            {approval.status === "pending" && (
                              <Button
                                onClick={() => handleReview(approval)}
                                className="whitespace-nowrap"
                              >
                                Review
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Request Information */}
                        <div className="space-y-3">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8 ring-2 ring-gray-100">
                              <AvatarImage
                                src={approval.requester.image || ""}
                              />
                              <AvatarFallback className="text-sm font-medium">
                                {approval.requester.name?.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {approval.requester.name}
                              </p>
                              <div className="mt-0.5 flex items-center gap-1 text-xs text-gray-500">
                                <IconClock className="h-4 w-4" />
                                <span>
                                  Requested <span>•</span>{" "}
                                  {formatDate(approval.submittedAt)}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Request Comments */}
                          {approval.comments && (
                            <div className="flex w-full items-start gap-3 rounded-xl border border-gray-200 bg-gray-50 p-3">
                              <div className="mt-0.5 flex-shrink-0">
                                <IconMessageCircle className="h-4 w-4 text-gray-500" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <p className="mb-1 text-sm font-medium text-gray-900">
                                  Request Comments
                                </p>
                                <p className="text-sm break-words text-gray-800">
                                  {approval.comments}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Review Information */}
                        {approval.status !== "pending" && (
                          <div className="mt-4 border-t border-gray-100 pt-4">
                            <div className="space-y-3">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8 ring-2 ring-gray-100">
                                  <AvatarImage
                                    src={approval.approver?.image || ""}
                                  />
                                  <AvatarFallback className="text-sm font-medium">
                                    {approval.approver?.name?.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">
                                    {approval.approver?.name}
                                  </p>
                                  <div className="mt-0.5 flex items-center gap-1 text-xs text-gray-500">
                                    <IconClock className="h-4 w-4" />
                                    <span className="flex items-center gap-1">
                                      {approval.status === "approved"
                                        ? "Approved"
                                        : "Rejected"}{" "}
                                      <span>•</span>
                                      {approval.reviewedAt
                                        ? formatDate(approval.reviewedAt)
                                        : ""}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Review Comments */}
                              {approval.reviewComments && (
                                <div className="flex w-full items-start gap-3 rounded-xl border border-gray-200 bg-gray-50 p-3">
                                  <div className="mt-0.5 flex-shrink-0">
                                    <IconMessageCircle className="h-4 w-4 text-gray-500" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <p className="mb-1 text-sm font-medium text-gray-900">
                                      Review Comments
                                    </p>
                                    <p className="text-sm break-words text-gray-800">
                                      {approval.reviewComments}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {isEmpty(data) && !noSearchResults && (
                <div className="py-60">
                  <EmptyState
                    title="No approval requests"
                    subtitle="When team members submit projects for approval, they'll appear here."
                  />
                </div>
              )}

              {noSearchResults && (
                <div className="py-60">
                  <EmptyState
                    title="No search results"
                    subtitle="Please check the spelling or filter criteria"
                  />
                </div>
              )}
            </>
          )}

          <div ref={ref} className="flex items-center justify-center">
            {approvals.isFetchingNextPage && <Loader className="mt-5" />}
          </div>
        </div>

        {selectedApproval && (
          <ApprovalReviewDialog
            approval={selectedApproval}
            open={reviewDialogOpen}
            onOpenChange={setReviewDialogOpen}
            onReview={async (approvalId, status, comments) =>
              await handleReviewSubmit(
                approvalId,
                status as ReviewMutationStatus,
                comments,
              )
            }
          />
        )}
      </PageContent>
    </PageWrapper>
  );
}
